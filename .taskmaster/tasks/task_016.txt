# Task ID: 16
# Title: Implement User Profile Management
# Status: pending
# Dependencies: 13
# Priority: low
# Description: Create user profile pages with settings, notification preferences, and account management
# Details:
1. Create Profile API endpoints:
```javascript
// routes/users.js
router.get('/profile', auth, async (req, res) => {
  const user = await User.findById(req.user.id)
    .select('-password');
  res.json(user);
});

router.put('/profile', auth, async (req, res) => {
  const updates = req.body;
  delete updates.password; // Prevent password update here
  
  const user = await User.findByIdAndUpdate(
    req.user.id,
    updates,
    { new: true }
  ).select('-password');
  
  res.json(user);
});
```
2. Create Profile component:
   - Personal information form
   - Avatar upload
   - Password change section
   - Notification preferences
   - Theme preferences
3. Add avatar upload with image optimization
4. Create account deletion flow
5. Add export user data functionality
6. Implement session management

# Test Strategy:
Test profile updates, verify avatar upload works, test password change flow, ensure notification preferences are saved correctly
