# Task ID: 10
# Title: Implement Search and Filter Functionality
# Status: pending
# Dependencies: 8
# Priority: medium
# Description: Build advanced search with filters for status, priority, tags, and date ranges with debounced search
# Details:
1. Create SearchBar component:
```typescript
// components/search/SearchBar.tsx
import { useDebouncedCallback } from 'use-debounce';

const SearchBar = () => {
  const dispatch = useDispatch();
  
  const debouncedSearch = useDebouncedCallback(
    (value: string) => {
      dispatch(searchTasks(value));
    },
    300
  );
  
  return (
    <input
      type="text"
      placeholder="Search tasks..."
      onChange={(e) => debouncedSearch(e.target.value)}
    />
  );
};
```
2. Create FilterPanel with:
   - Status checkboxes
   - Priority radio buttons
   - Tag multi-select
   - Date range picker
3. Implement filter combination logic
4. Add saved filter presets
5. Create clear filters button
6. Show active filter badges

# Test Strategy:
Test search debouncing, verify filter combinations work correctly, test performance with large datasets, ensure filter persistence
