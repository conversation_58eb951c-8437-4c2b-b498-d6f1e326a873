# Task ID: 5
# Title: Implement Task CRUD Operations API
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Build RESTful API endpoints for creating, reading, updating, and deleting tasks with proper validation and authorization
# Details:
1. Create task controller:
```javascript
// controllers/taskController.js
exports.createTask = async (req, res) => {
  const { title, description, dueDate, priority, tags } = req.body;
  const task = new Task({
    title,
    description,
    dueDate,
    priority,
    tags,
    owner: req.user.id
  });
  await task.save();
  res.status(201).json(task);
};

exports.getTasks = async (req, res) => {
  const { status, priority, tags, search } = req.query;
  const query = { owner: req.user.id };
  
  // Build dynamic query
  if (status) query.status = status;
  if (priority) query.priority = priority;
  if (tags) query.tags = { $in: tags.split(',') };
  if (search) query.$text = { $search: search };
  
  const tasks = await Task.find(query)
    .populate('assignees', 'username email')
    .sort({ dueDate: 1 });
  res.json(tasks);
};

exports.updateTask = async (req, res) => {
  const task = await Task.findOneAndUpdate(
    { _id: req.params.id, owner: req.user.id },
    { ...req.body, updatedAt: Date.now() },
    { new: true }
  );
  if (!task) return res.status(404).json({ error: 'Task not found' });
  res.json(task);
};

exports.deleteTask = async (req, res) => {
  const task = await Task.findOneAndDelete({
    _id: req.params.id,
    owner: req.user.id
  });
  if (!task) return res.status(404).json({ error: 'Task not found' });
  res.json({ message: 'Task deleted' });
};
```
2. Add validation rules for each endpoint
3. Implement pagination for getTasks
4. Add sorting options (by date, priority, status)
5. Create batch operations endpoint

# Test Strategy:
Test CRUD operations with valid/invalid data, verify authorization checks, test pagination and filtering, ensure proper error responses
