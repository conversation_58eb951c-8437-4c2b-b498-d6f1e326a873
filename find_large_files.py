#!/usr/bin/env python3
"""
查找用户目录下最大文件的脚本
适用于 macOS 系统
功能: 快速扫描用户目录，找出最大的20个文件
"""

import os
import sys
from pathlib import Path
import heapq
from datetime import datetime

def format_size(size_bytes):
    """将字节转换为人类可读的格式"""
    if size_bytes >= 1024**3:  # GB
        return f"{size_bytes / (1024**3):.2f} GB"
    elif size_bytes >= 1024**2:  # MB
        return f"{size_bytes / (1024**2):.2f} MB"
    elif size_bytes >= 1024:  # KB
        return f"{size_bytes / 1024:.2f} KB"
    else:
        return f"{size_bytes} B"

def should_skip_directory(dir_name, full_path):
    """判断是否应该跳过某个目录"""
    # 跳过所有隐藏目录（以.开头）
    if dir_name.startswith('.'):
        return True

    # 系统目录和应用程序目录列表
    skip_dirs = {
        # macOS 系统目录
        'Library', 'System', 'Applications', 'Developer',

        # 常见的缓存和临时目录
        'Caches', 'Logs', 'Trash', 'Cache', 'Temp', 'tmp',

        # 开发相关目录
        'node_modules', '__pycache__', '.git', '.svn', '.hg',
        'venv', '.venv', 'env', '.env', 'virtualenv',
        'build', 'dist', '.gradle', '.maven', 'target',

        # 编译器和IDE目录
        '.vscode', '.idea', '.eclipse', '.xcode',

        # 包管理器目录
        'bower_components', 'vendor', 'packages',

        # 数据库和日志目录
        'logs', 'log', 'database', 'db',

        # 备份目录
        'backup', 'backups', '.backup',

        # 虚拟机和容器目录
        '.docker', '.vagrant', 'VirtualBox VMs',

        # 浏览器相关目录
        'Google', 'Mozilla', 'Safari', 'Chrome', 'Firefox',

        # 其他常见的系统应用目录
        'Mail', 'Messages', 'Photos', 'Music', 'iTunes',
        'Spotify', 'Steam', 'Games'
    }

    # 检查目录名是否在跳过列表中
    if dir_name in skip_dirs:
        return True

    # 检查是否是应用程序包（.app结尾）
    if dir_name.endswith('.app'):
        return True

    # 检查特定路径模式
    if 'Library' in full_path and any(x in full_path for x in [
        'Caches', 'Logs', 'Application Support', 'Containers',
        'Group Containers', 'Saved Application State'
    ]):
        return True

    return False

def scan_directory(directory_path, top_n=20):
    """扫描目录并返回最大的N个文件"""
    print(f"正在扫描目录: {directory_path}")
    print("已配置跳过系统目录和应用程序目录...")
    print("这可能需要几分钟时间，请耐心等待...")
    print()

    # 使用最小堆来维护最大的N个文件
    # 堆中存储 (size, filepath) 元组
    min_heap = []
    file_count = 0
    skipped_dirs = 0

    try:
        # 遍历所有文件
        for root, dirs, files in os.walk(directory_path):
            # 过滤需要跳过的目录
            original_dirs_count = len(dirs)
            dirs[:] = [d for d in dirs if not should_skip_directory(d, os.path.join(root, d))]
            skipped_dirs += original_dirs_count - len(dirs)

            for file in files:
                file_path = os.path.join(root, file)
                try:
                    # 跳过隐藏文件和系统文件
                    if file.startswith('.') or file.startswith('~'):
                        continue

                    # 跳过一些常见的系统文件类型
                    skip_extensions = {'.tmp', '.log', '.cache', '.lock', '.pid', '.sock'}
                    if any(file.lower().endswith(ext) for ext in skip_extensions):
                        continue

                    # 获取文件大小
                    size = os.path.getsize(file_path)
                    file_count += 1

                    # 显示进度
                    if file_count % 1000 == 0:
                        print(f"已扫描 {file_count} 个文件，跳过 {skipped_dirs} 个目录...", end='\r')

                    # 维护最大的N个文件
                    if len(min_heap) < top_n:
                        heapq.heappush(min_heap, (size, file_path))
                    elif size > min_heap[0][0]:
                        heapq.heapreplace(min_heap, (size, file_path))

                except (OSError, IOError, PermissionError):
                    # 跳过无法访问的文件
                    continue

    except KeyboardInterrupt:
        print("\n扫描被用户中断")
        return []
    except Exception as e:
        print(f"\n扫描过程中出现错误: {e}")
        return []

    print(f"\n扫描完成！")
    print(f"共扫描了 {file_count} 个文件")
    print(f"跳过了 {skipped_dirs} 个系统/应用程序目录")

    # 将堆转换为按大小降序排列的列表
    largest_files = sorted(min_heap, key=lambda x: x[0], reverse=True)
    return largest_files

def main():
    """主函数"""
    print("=" * 70)
    print("用户目录大文件查找工具 (智能过滤版)")
    print("=" * 70)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("注意: 此工具会自动跳过以下目录以提高扫描效率:")
    print("• 系统目录 (Library, System, Applications 等)")
    print("• 应用程序包 (.app 文件)")
    print("• 开发环境目录 (node_modules, .git, venv 等)")
    print("• 缓存和临时目录 (Caches, Logs, Temp 等)")
    print("• 隐藏目录和文件 (以 . 开头)")
    print()
    
    # 获取用户主目录
    home_dir = Path.home()
    
    # 扫描并获取最大的20个文件
    largest_files = scan_directory(str(home_dir), 20)
    
    if not largest_files:
        print("未找到任何文件或扫描被中断")
        return
    
    # 显示结果
    print()
    print("=" * 90)
    print("用户目录下最大的 20 个文件 (已过滤系统文件):")
    print("=" * 90)
    print(f"{'序号':<4} {'文件大小':<12} {'文件路径'}")
    print("-" * 90)
    
    total_size = 0
    for i, (size, filepath) in enumerate(largest_files, 1):
        total_size += size
        print(f"{i:<4} {format_size(size):<12} {filepath}")
    
    print("-" * 90)
    print(f"前20个文件总大小: {format_size(total_size)}")
    print()

    # 提供使用建议
    print("💡 使用建议:")
    print("1. 这些文件已经过滤掉了系统文件，相对安全")
    print("2. 删除前仍建议先备份重要数据")
    print("3. 推荐在 Finder 中预览文件内容再决定是否删除")
    print("4. 删除方法:")
    print("   • Finder: Cmd+Shift+G 输入路径，然后删除")
    print("   • 命令行: rm '文件路径'")
    print()
    print("⚠️  注意: 即使过滤了系统文件，删除前仍需谨慎确认")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        sys.exit(1)
