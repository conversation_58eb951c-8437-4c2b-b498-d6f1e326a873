#!/usr/bin/env python3
"""
查找用户目录下最大文件的脚本
适用于 macOS 系统
功能: 快速扫描用户目录，找出最大的20个文件
"""

import os
import sys
from pathlib import Path
import heapq
from datetime import datetime

def format_size(size_bytes):
    """将字节转换为人类可读的格式"""
    if size_bytes >= 1024**3:  # GB
        return f"{size_bytes / (1024**3):.2f} GB"
    elif size_bytes >= 1024**2:  # MB
        return f"{size_bytes / (1024**2):.2f} MB"
    elif size_bytes >= 1024:  # KB
        return f"{size_bytes / 1024:.2f} KB"
    else:
        return f"{size_bytes} B"

def scan_directory(directory_path, top_n=20):
    """扫描目录并返回最大的N个文件"""
    print(f"正在扫描目录: {directory_path}")
    print("这可能需要几分钟时间，请耐心等待...")
    print()
    
    # 使用最小堆来维护最大的N个文件
    # 堆中存储 (size, filepath) 元组
    min_heap = []
    file_count = 0
    
    try:
        # 遍历所有文件
        for root, dirs, files in os.walk(directory_path):
            # 跳过一些系统目录和隐藏目录以提高速度
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in [
                'Library/Caches', 'Library/Logs', '.Trash', 'node_modules', 
                '.git', '__pycache__', '.venv', 'venv'
            ]]
            
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    # 获取文件大小
                    size = os.path.getsize(file_path)
                    file_count += 1
                    
                    # 显示进度
                    if file_count % 1000 == 0:
                        print(f"已扫描 {file_count} 个文件...", end='\r')
                    
                    # 维护最大的N个文件
                    if len(min_heap) < top_n:
                        heapq.heappush(min_heap, (size, file_path))
                    elif size > min_heap[0][0]:
                        heapq.heapreplace(min_heap, (size, file_path))
                        
                except (OSError, IOError):
                    # 跳过无法访问的文件
                    continue
                    
    except KeyboardInterrupt:
        print("\n扫描被用户中断")
        return []
    except Exception as e:
        print(f"\n扫描过程中出现错误: {e}")
        return []
    
    print(f"\n扫描完成！共扫描了 {file_count} 个文件")
    
    # 将堆转换为按大小降序排列的列表
    largest_files = sorted(min_heap, key=lambda x: x[0], reverse=True)
    return largest_files

def main():
    """主函数"""
    print("=" * 60)
    print("用户目录大文件查找工具")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 获取用户主目录
    home_dir = Path.home()
    
    # 扫描并获取最大的20个文件
    largest_files = scan_directory(str(home_dir), 20)
    
    if not largest_files:
        print("未找到任何文件或扫描被中断")
        return
    
    # 显示结果
    print()
    print("=" * 80)
    print("用户目录下最大的 20 个文件:")
    print("=" * 80)
    print(f"{'序号':<4} {'文件大小':<12} {'文件路径'}")
    print("-" * 80)
    
    total_size = 0
    for i, (size, filepath) in enumerate(largest_files, 1):
        total_size += size
        print(f"{i:<4} {format_size(size):<12} {filepath}")
    
    print("-" * 80)
    print(f"前20个文件总大小: {format_size(total_size)}")
    print()
    
    # 提供使用建议
    print("使用建议:")
    print("1. 删除文件前请确认文件不是系统重要文件")
    print("2. 建议先备份重要数据")
    print("3. 可以在 Finder 中找到文件进行删除")
    print("4. 或使用命令行: rm '文件路径'")
    print()
    print("注意: 请谨慎删除系统文件和应用程序文件")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        sys.exit(1)
