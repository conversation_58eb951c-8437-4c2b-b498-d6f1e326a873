# Task ID: 9
# Title: Create Dashboard with Data Visualization
# Status: pending
# Dependencies: 8
# Priority: medium
# Description: Build dashboard page with task statistics, charts using Chart.js, and summary widgets
# Details:
1. Install and configure Chart.js:
```bash
npm install react-chartjs-2 chart.js
```
2. Create Dashboard component:
```typescript
// pages/Dashboard.tsx
import { Doughnut, Bar } from 'react-chartjs-2';

const Dashboard = () => {
  const stats = useSelector(selectTaskStats);
  
  const statusChartData = {
    labels: ['Pending', 'In Progress', 'Completed'],
    datasets: [{
      data: [stats.pending, stats.inProgress, stats.completed],
      backgroundColor: ['#ff6384', '#36a2eb', '#4bc0c0']
    }]
  };
  
  return (
    <div className="dashboard">
      <SummaryCards stats={stats} />
      <Doughnut data={statusChartData} />
      <UpcomingTasks />
      <OverdueTasks />
    </div>
  );
};
```
3. Create summary widgets:
   - Total tasks count
   - Completion rate
   - Tasks due today/this week
   - Overdue tasks alert
4. Add priority distribution chart
5. Implement date range filter
6. Create exportable reports

# Test Strategy:
Verify chart data accuracy, test responsive behavior, ensure real-time updates when tasks change, test date filtering
