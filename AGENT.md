# AGENT.md - Agent Development Guidelines

## Build/Test Commands
- Run Python script: `python3 find_large_files.py` or `./find_large_files.py`
- Run Shell script: `./find_large_files.sh`
- Test execution: Run scripts on test directory to verify functionality
- No formal test suite - manual testing by running on sample directories

## Architecture & Structure
- **Demo project**: File size analysis utilities for macOS
- **Main components**: Two file scanning scripts (Python/Shell) for finding largest files
- **Task management**: Uses Task Master CLI (`task-master`) for project workflow
- **Configuration**: `.taskmaster/config.json` with OpenRouter/Claude model settings
- **No databases**: Filesystem-only operations, no persistent storage

## Code Style Guidelines
- **Python**: Follow PEP 8, use type hints, descriptive function names, comprehensive docstrings
- **Shell**: Use `#!/bin/bash`, quote variables, include error handling with `2>/dev/null`
- **Error handling**: Graceful handling of permission errors and inaccessible files
- **File naming**: Use snake_case, descriptive names (`find_large_files.py`)
- **Comments**: Include purpose and functionality descriptions, especially for complex logic
- **Chinese localization**: User-facing messages in Chinese, code/variables in English

## Conventions from .windsurfrules
- Use Task Master workflow: `task-master list`, `task-master next` for development
- Follow structured rule format with descriptions, globs, and examples
- Include both positive and negative code examples (✅ DO / ❌ DON'T)
- Reference existing code patterns when adding new functionality
