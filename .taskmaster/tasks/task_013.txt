# Task ID: 13
# Title: Add Email Notification Service
# Status: pending
# Dependencies: 5
# Priority: medium
# Description: Implement email notifications for task deadlines and updates using a service like SendGrid or AWS SES
# Details:
1. Setup email service:
```javascript
// services/emailService.js
const sgMail = require('@sendgrid/mail');
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

class EmailService {
  async sendDeadlineReminder(user, task) {
    const msg = {
      to: user.email,
      from: '<EMAIL>',
      subject: `Reminder: ${task.title} is due soon`,
      html: this.getDeadlineTemplate(user, task)
    };
    await sgMail.send(msg);
  }
  
  async sendTaskAssignment(assignee, task, assigner) {
    // Send assignment notification
  }
}
```
2. Create email templates:
   - Deadline reminder
   - Task assignment
   - Task completion
   - Daily digest
3. Implement notification preferences:
```javascript
// models/User.js additions
notificationPreferences: {
  deadlineReminders: { type: Boolean, default: true },
  assignmentAlerts: { type: Boolean, default: true },
  dailyDigest: { type: Boolean, default: false },
  reminderTime: { type: Number, default: 24 } // hours before deadline
}
```
4. Create cron job for scheduled emails
5. Add unsubscribe functionality
6. Track email delivery status

# Test Strategy:
Test email delivery with test accounts, verify template rendering, test cron job scheduling, ensure unsubscribe works properly
