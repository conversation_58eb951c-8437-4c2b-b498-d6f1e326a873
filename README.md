# 大文件查找工具

这个工具包含两个脚本，用于在 macOS 系统上快速查找用户目录下最大的文件，帮助您释放磁盘空间。

## 文件说明

### 1. find_large_files.sh (Shell 脚本版本)
- 使用 bash 编写
- 适合所有 macOS 系统
- 使用系统自带的 `find` 和 `du` 命令

### 2. find_large_files.py (Python 脚本版本) - **推荐**
- 使用 Python 3 编写
- 性能更好，扫描速度更快
- 提供更详细的进度显示
- 自动跳过一些不必要的系统目录

## 使用方法

### 方法一：运行 Shell 脚本
```bash
./find_large_files.sh
```

### 方法二：运行 Python 脚本（推荐）
```bash
./find_large_files.py
```
或者
```bash
python3 find_large_files.py
```

## 功能特点

- ✅ 扫描整个用户目录（~/）
- ✅ 找出最大的 20 个文件
- ✅ 按文件大小从大到小排序
- ✅ 显示文件大小（自动转换为 GB/MB/KB）
- ✅ 显示完整文件路径
- ✅ 提供删除建议和安全提示
- ✅ 跳过系统缓存和临时目录（Python 版本）
- ✅ 实时进度显示（Python 版本）

## 输出示例

```
用户目录下最大的 20 个文件:
================================================================================
序号 文件大小      文件路径
--------------------------------------------------------------------------------
1    2.34 GB     /Users/<USER>/Movies/large_video.mp4
2    1.87 GB     /Users/<USER>/Downloads/software.dmg
3    856.23 MB   /Users/<USER>/Documents/presentation.pptx
...
```

## 安全提示

⚠️ **删除文件前请注意：**
1. 确认文件不是系统重要文件
2. 建议先备份重要数据
3. 谨慎删除应用程序文件
4. 可以在 Finder 中预览文件内容再决定是否删除

## 删除文件的方法

### 方法一：使用 Finder（推荐）
1. 复制脚本输出的文件路径
2. 在 Finder 中按 `Cmd+Shift+G`
3. 粘贴路径并前往
4. 右键点击文件选择"移到废纸篓"

### 方法二：使用命令行
```bash
rm "文件完整路径"
```

## 系统要求

- macOS 系统
- Python 3（用于 Python 版本）
- 对用户目录的读取权限

## 性能说明

- Shell 版本：适合文件数量较少的情况
- Python 版本：推荐使用，性能更好，适合大量文件扫描
- 扫描时间取决于文件数量，通常需要几分钟

## 故障排除

如果遇到权限问题，可能需要：
1. 在"系统偏好设置" > "安全性与隐私" > "隐私"中授权终端访问文件
2. 或者使用 `sudo` 运行脚本（不推荐）

## 注意事项

- 脚本会自动跳过无法访问的文件
- 不会修改或删除任何文件，只是查找和显示
- 可以随时按 `Ctrl+C` 中断扫描
