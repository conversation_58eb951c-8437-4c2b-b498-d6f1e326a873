# Task ID: 19
# Title: Implement Security Best Practices
# Status: pending
# Dependencies: 17
# Priority: high
# Description: Add security measures including rate limiting, input sanitization, HTTPS, and security headers
# Details:
1. Implement rate limiting:
```javascript
// middleware/rateLimiter.js
const rateLimit = require('express-rate-limit');

exports.authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 requests per window
  message: 'Too many login attempts'
});

exports.apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100
});
```
2. Add input sanitization:
```javascript
// middleware/sanitize.js
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');

app.use(mongoSanitize());
app.use(xss());
```
3. Configure security headers:
```javascript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```
4. Implement CSRF protection
5. Add SQL injection prevention
6. Setup security monitoring

# Test Strategy:
Test rate limiting effectiveness, verify XSS protection, test CSRF tokens, run security vulnerability scans, test with OWASP ZAP
