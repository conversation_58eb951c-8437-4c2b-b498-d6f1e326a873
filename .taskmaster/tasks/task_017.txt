# Task ID: 17
# Title: Add Performance Optimization
# Status: pending
# Dependencies: 15
# Priority: medium
# Description: Implement pagination, lazy loading, caching, and query optimization for better performance
# Details:
1. Implement backend pagination:
```javascript
// controllers/taskController.js
exports.getTasks = async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;
  
  const tasks = await Task.find(query)
    .skip(skip)
    .limit(limit)
    .lean(); // Use lean() for better performance
    
  const total = await Task.countDocuments(query);
  
  res.json({
    tasks,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  });
};
```
2. Add Redis caching:
```javascript
// middleware/cache.js
const redis = require('redis');
const client = redis.createClient();

exports.cacheMiddleware = (duration) => {
  return async (req, res, next) => {
    const key = `cache:${req.originalUrl}`;
    const cached = await client.get(key);
    
    if (cached) {
      return res.json(JSON.parse(cached));
    }
    
    res.sendResponse = res.json;
    res.json = (body) => {
      client.setex(key, duration, JSON.stringify(body));
      res.sendResponse(body);
    };
    next();
  };
};
```
3. Implement React.lazy for code splitting
4. Add virtual scrolling for long lists
5. Optimize database queries with indexes
6. Implement request debouncing

# Test Strategy:
Test pagination with large datasets, verify caching improves response times, measure bundle size reduction, test lazy loading behavior
