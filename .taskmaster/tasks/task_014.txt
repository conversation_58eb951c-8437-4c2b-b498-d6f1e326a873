# Task ID: 14
# Title: Implement Task Sharing and Collaboration
# Status: pending
# Dependencies: 5, 13
# Priority: medium
# Description: Build functionality to share tasks with other users, assign tasks, and manage permissions
# Details:
1. Update Task model for sharing:
```javascript
// Additional fields in Task schema
sharedWith: [{
  user: { type: Schema.Types.ObjectId, ref: 'User' },
  permission: { type: String, enum: ['view', 'edit'], default: 'view' },
  sharedAt: { type: Date, default: Date.now }
}]
```
2. Create sharing API endpoints:
```javascript
// routes/tasks.js
router.post('/:id/share', auth, async (req, res) => {
  const { userId, permission } = req.body;
  const task = await Task.findById(req.params.id);
  
  if (task.owner.toString() !== req.user.id) {
    return res.status(403).json({ error: 'Not authorized' });
  }
  
  task.sharedWith.push({ user: userId, permission });
  await task.save();
  
  // Send notification email
  await emailService.sendTaskAssignment(userId, task, req.user);
  
  res.json(task);
});
```
3. Create ShareModal component:
   - User search/selection
   - Permission level selector
   - Current collaborators list
4. Update task queries to include shared tasks
5. Add activity tracking for shared tasks
6. Implement permission checks in UI

# Test Strategy:
Test sharing with different permission levels, verify email notifications are sent, test permission enforcement, ensure shared tasks appear correctly
