# Task ID: 3
# Title: Implement JWT Authentication System
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Build secure user authentication with JWT tokens, including registration, login, and password hashing using bcrypt
# Details:
1. Install packages: jsonwebtoken, bcryptjs, express-validator
2. Create authentication middleware:
```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');
module.exports = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  if (!token) return res.status(401).json({ error: 'Access denied' });
  
  try {
    const verified = jwt.verify(token, process.env.JWT_SECRET);
    req.user = verified;
    next();
  } catch (err) {
    res.status(400).json({ error: 'Invalid token' });
  }
};
```
3. Implement auth routes:
```javascript
// routes/auth.js
router.post('/register', async (req, res) => {
  // Validate input
  // Hash password with bcrypt (salt rounds: 10)
  // Create user in database
  // Generate JWT token
});

router.post('/login', async (req, res) => {
  // Validate credentials
  // Compare password with bcrypt
  // Generate JWT token with user ID
});

router.post('/refresh-token', async (req, res) => {
  // Implement refresh token logic
});
```
4. Add password reset functionality with email tokens
5. Implement rate limiting for auth endpoints

# Test Strategy:
Test registration with valid/invalid data, login with correct/incorrect credentials, JWT token validation, password hashing strength, and rate limiting effectiveness
