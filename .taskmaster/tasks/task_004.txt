# Task ID: 4
# Title: Create Express Backend API Structure
# Status: pending
# Dependencies: 3
# Priority: high
# Description: Setup Express server with proper middleware, routing, error handling, and RESTful API endpoints structure
# Details:
1. Setup Express server:
```javascript
// server.js
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const app = express();

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/users', userRoutes);
app.use('/api/comments', commentRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(err.status || 500).json({
    error: err.message || 'Internal server error'
  });
});
```
2. Create route structure with proper HTTP methods
3. Implement request validation middleware
4. Add API versioning (/api/v1/)
5. Setup logging with Winston
6. Configure CORS for frontend origin

# Test Strategy:
Test all API endpoints with Postman/Jest, verify proper status codes, error handling, and middleware execution order
