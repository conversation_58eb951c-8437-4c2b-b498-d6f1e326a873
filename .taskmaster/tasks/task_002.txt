# Task ID: 2
# Title: Setup MongoDB Database Schema and Connection
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Design and implement the MongoDB database schema for User, Task, and Comment models with proper indexing and relationships
# Details:
1. Install mongoose and mongodb packages
2. Create database connection module:
```javascript
// db/connection.js
const mongoose = require('mongoose');
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});
```
3. Define schemas:
```javascript
// models/User.js
const userSchema = new Schema({
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  profile: {
    firstName: String,
    lastName: String,
    avatar: String
  },
  createdAt: { type: Date, default: Date.now }
});

// models/Task.js
const taskSchema = new Schema({
  title: { type: String, required: true },
  description: String,
  status: { type: String, enum: ['pending', 'in-progress', 'completed'], default: 'pending' },
  priority: { type: String, enum: ['high', 'medium', 'low'], default: 'medium' },
  dueDate: Date,
  owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  assignees: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  tags: [String],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// models/Comment.js
const commentSchema = new Schema({
  content: { type: String, required: true },
  author: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  task: { type: Schema.Types.ObjectId, ref: 'Task', required: true },
  timestamp: { type: Date, default: Date.now }
});
```
4. Add indexes for performance
5. Create seed data script for development

# Test Strategy:
Write unit tests to verify database connection, schema validation, and CRUD operations for each model. Test indexing performance with sample data
