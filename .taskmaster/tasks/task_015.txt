# Task ID: 15
# Title: Build Comments System
# Status: pending
# Dependencies: 14
# Priority: medium
# Description: Create commenting functionality for tasks with real-time updates and activity tracking
# Details:
1. Create Comment API:
```javascript
// controllers/commentController.js
exports.addComment = async (req, res) => {
  const { content, taskId } = req.body;
  
  // Verify user has access to task
  const task = await Task.findById(taskId);
  const hasAccess = task.owner.equals(req.user.id) || 
    task.sharedWith.some(s => s.user.equals(req.user.id));
    
  if (!hasAccess) {
    return res.status(403).json({ error: 'Access denied' });
  }
  
  const comment = new Comment({
    content,
    author: req.user.id,
    task: taskId
  });
  
  await comment.save();
  await comment.populate('author', 'username avatar');
  
  res.status(201).json(comment);
};
```
2. Create Comments component:
```typescript
// components/comments/CommentSection.tsx
const CommentSection = ({ taskId }) => {
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  
  return (
    <div className="comment-section">
      <CommentList comments={comments} />
      <CommentForm 
        value={newComment}
        onChange={setNewComment}
        onSubmit={handleSubmit}
      />
    </div>
  );
};
```
3. Add real-time updates with Socket.io
4. Implement comment editing/deletion
5. Add @mentions functionality
6. Create activity feed component

# Test Strategy:
Test comment CRUD operations, verify real-time updates work, test permission checks, ensure activity tracking is accurate
